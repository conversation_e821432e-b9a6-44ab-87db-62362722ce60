<!--
 * @Description: 问卷管理
 * @Autor: Fhz
 * @Date: 2025-02-11 15:30:59
 * @LastEditors: panmy
 * @LastEditTime: 2025-08-04 14:21:31
-->
<template>
  <Spin :spinning="loading" size="large">
    <div class="mr-20px ml-20px">
      <!-- 页面搜索区 -->
      <div class="fw-tip">
        <Mtcn-Input v-model="keyWord" placeholder="请输入关键词搜索" />
        <a-button @click="fetchData" type="primary">搜索</a-button>
        <a-button @click="handleResset">重置</a-button>
      </div>
      <div class="fw-tip">
        <a-button type="primary" @click="handleAddOrUpdate({})">新增</a-button>
      </div>
      <div class="fw-tip"> 已创建{{ batchList.length }}个困难生类型</div>

      <div class="portalMain">
        <template v-for="(item, index) in batchList" :key="index">
          <div :class="['card', item.status ? 'cardSel' : '']">
            <div class="cardTop">
              <!-- 卡片标题区 -->
              <p class="cardTitle">{{ item.title }}</p>
            </div>
            <!-- 卡片按钮组区 -->
            <div class="cardOpts">
              <p class="link" @click="handleAddOrUpdate(item)">编辑</p>
              <div class="split"></div>
              <p class="link" @click="handleDelete(item)">删除</p>
              <div class="split"></div>
              <a-switch v-model:checked="item.isUsed" checked-children="启用" un-checked-children="停用" @change="handleStatusChange(item)"></a-switch>
            </div>
          </div>
        </template>
      </div>
    </div>
  </Spin>
  <Form @register="registerModal" @reload="fetchData" />
</template>
<script lang="ts" setup>
  import { ref, onMounted } from 'vue';
  import { useModal } from '@/components/Modal';
  import { Spin, Empty } from 'ant-design-vue';
  import { DownOutlined } from '@ant-design/icons-vue';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useBaseApi } from '@/hooks/web/useBaseApi';
  import Form from './Form.vue';

  const [registerModal, { openModal }] = useModal();
  const { createMessage, createConfirm } = useMessage();

  // 初始化API
  const api = useBaseApi('/api/kns/jt/knlx');

  const batchList = ref([]);
  const loading = ref(false);
  const keyWord = ref('');

  function handleAddOrUpdate(record) {
    openModal(true, { id: record.id || '' });
  }

  function handleDelete(record) {
    createConfirm({
      iconType: 'warning',
      title: '提示',
      content: '您确定要删除该困难类型吗？',
      onOk: async () => {
        try {
          const { msg } = await api.request('delete', `/remove/${record.id}`, { isFullPath: false });
          createMessage.success(msg || '删除成功');
          fetchData();
        } catch (error) {
          console.error('删除失败:', error);
          createMessage.error('删除失败');
        }
      },
    });
  }

  function handleResset() {
    keyWord.value = '';
    fetchData();
  }

  async function fetchData() {
    loading.value = true;
    try {
      const { data } = await api.request('get', '/listAll', {
        params: {
          keyword: keyWord.value,
        },
        isFullPath: false,
      });

      // 处理数据，转换字段名和数据类型
      const processedList = (data || []).map(item => ({
        id: item.id,
        title: item.jtknlxmc, // 家庭困难类型名称
        description: item.jtknlxsm, // 困难类型说明
        isUsed: Boolean(Number(item.sfsy || 0)), // 是否使用，转换为布尔值
        status: Boolean(Number(item.sfsy || 0)), // 兼容现有模板中的status字段
        sortCode: item.sortCode || 0, // 排序
        jtknlxdm: item.jtknlxdm, // 家庭困难类型代码
        creatorTime: item.creatorTime,
        lastModifyTime: item.lastModifyTime,
      }));

      batchList.value = processedList;
    } catch (error) {
      console.error('获取数据失败:', error);
      createMessage.error('获取数据失败');
      batchList.value = [];
    } finally {
      loading.value = false;
    }
  }

  // 处理启用/停用状态切换
  async function handleStatusChange(record) {
    try {
      const newStatus = record.isUsed ? '1' : '0';
      const { msg } = await api.request('put', `/editBySfsy/${record.id}`, {
        params: { sfsy: newStatus },
        isFullPath: false,
      });
      createMessage.success(msg || '状态更新成功');
      // 更新本地数据
      record.status = record.isUsed;
    } catch (error) {
      console.error('状态更新失败:', error);
      createMessage.error('状态更新失败');
      // 回滚状态
      record.isUsed = !record.isUsed;
    }
  }

  onMounted(() => {
    fetchData();
  });
</script>
<style lang="less" scoped>
  .mtcn-content-wrapper-content {
    background: #fff;
    position: relative;

    overflow: scroll !important;
    .empty {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
  .fw-tip {
    margin: 10px 0;
    color: #666666;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 10px;
    .ant-input {
      width: 300px;
    }
  }
  .remark {
    color: #666666;
    font-size: 12px;
  }
  .portalMain {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));

    margin: 20px 0;
    grid-gap: 10px;
    .card {
      border: 1px solid #edeff2;
      border-radius: 4px;
      position: relative;
      overflow: hidden;
      .cardTop {
        padding: 16px;
        .cardTitle {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          word-break: break-all;
          font-size: 16px;
          // font-weight: 700;
          margin-bottom: 16px;
        }
        .cardTemplate {
          margin-top: 4px;
          color: #86909c;
          font-size: 14px;
          line-height: 22px;
          span {
            color: #4e5969;
          }
        }
      }
      .cardOpts {
        padding: 11px 12px;
        border-top: 1px solid #edeff2;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-around;

        .link {
          border-color: transparent;
          color: @primary-color;
          background: 0 0;
          display: inline-block;
          line-height: 1;
          white-space: nowrap;
          cursor: pointer;
        }
        .split {
          width: 1px;
          height: 12px;
          margin: 0 12px;
          background-color: #dfe2e8;
          flex-shrink: 0;
        }
      }
    }
    .cardSel::after {
      content: '已启用';
      top: 10px;
      right: -20px;
      width: 80px;
      height: 20px;
      line-height: 20px;
      position: absolute;
      transform: rotate(45deg);
      font-size: 12px;
      text-align: center;
      color: #fff;
      background: #00b42a;
    }
  }
</style>
